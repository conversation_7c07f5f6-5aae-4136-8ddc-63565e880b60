"use client";

import SideNav from '@/app/ui/dashboard/sidenav';
import TopNav from '@/app/ui/dashboard/topnav';
import { useState, useEffect } from 'react';
import { ChevronLeft, Menu } from 'lucide-react';

export default function Layout({ children }: { children: React.ReactNode }) {
  // Always default to open sidebar - do not auto hide
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(false);

  // Check if we're on mobile for responsive styling, but don't auto-close sidebar
  useEffect(() => {
    const checkIfMobile = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      // Removed auto-close functionality - sidebar stays open regardless of screen size
    };

    // Initial check
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  // Ensure page starts at the top when loading
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const closeSidebar = () => {
    setIsSidebarOpen(false);
  };

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <div className="flex min-h-screen flex-col md:flex-row">
      {/* Toggle sidebar button - visible on all screen sizes */}
      <button
        className={`fixed top-4 ${isMobile ? 'left-4' : isSidebarOpen ? 'left-[260px]' : 'left-4'} p-2 z-50 bg-white rounded-full shadow-md hover:bg-gray-100 transition-all duration-300`}
        onClick={toggleSidebar}
        aria-label="Toggle sidebar"
        style={{ display: 'none' }} // Hide this button as we'll move it to the TopNav
      >
        {isSidebarOpen ? (
          <ChevronLeft className="w-5 h-5" />
        ) : (
          <Menu className="w-5 h-5" />
        )}
      </button>

      {/* Sidebar Navigation */}
      <nav
        className={`
          fixed md:relative h-screen transform transition-all duration-300 ease-in-out z-40 overflow-hidden
          ${isSidebarOpen ? 'translate-x-0 w-full md:w-64' : '-translate-x-full w-0'}
        `}
        role="navigation"
        aria-label="Main navigation"
      >
        <SideNav closeSidebar={closeSidebar} />
      </nav>

      {/* Main content */}
      <div className="flex flex-1 flex-col w-full m-0 font-sans antialiased font-normal text-left leading-default text-base bg-gray-50 text-slate-500">

        <TopNav
          isSidebarOpen={isSidebarOpen}
          toggleSidebar={toggleSidebar}
          isMobile={isMobile}
        />
        <main
          id="main-content"
          className={`flex-1 overflow-y-auto p-4 md:p-6 transition-all duration-300 ${isSidebarOpen ? 'md:ml-0' : 'ml-0'}`}
          role="main"
        >
          {children}
        </main>
      </div>

      {/* Overlay for mobile - only show when sidebar is open and on mobile */}
      {isSidebarOpen && isMobile && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30"
          onClick={closeSidebar}
        />
      )}
    </div>
  );
}
