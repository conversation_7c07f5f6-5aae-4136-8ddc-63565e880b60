{"private": true, "scripts": {"build": "next build", "dev": "next dev --turbopack", "start": "next start"}, "dependencies": {"@headlessui/react": "^2.2.1", "@heroicons/react": "^2.2.0", "@node-rs/bcrypt": "^1.10.7", "@radix-ui/react-avatar": "^1.1.6", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@tailwindcss/forms": "^0.5.10", "@types/bcryptjs": "^3.0.0", "autoprefixer": "10.4.20", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "filepond": "^4.32.7", "formidable": "^3.5.4", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "lucide-react": "^0.486.0", "next": "^15.3.0", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "openai": "^4.87.3", "pdf2json": "^3.1.5", "postcss": "8.5.1", "postgres": "^3.4.5", "quill": "^2.0.3", "react": "latest", "react-day-picker": "^8.10.1", "react-dom": "latest", "react-filepond": "^7.1.3", "react-hook-form": "^7.55.0", "react-quill-new": "^3.4.6", "reactflow": "^11.11.4", "recharts": "^2.15.2", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0", "tailwindcss": "3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "5.7.3", "use-debounce": "^10.0.4", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/formidable": "^3.4.5", "@types/js-cookie": "^3.0.6", "@types/node": "22.10.7", "@types/react": "19.0.7", "@types/react-dom": "19.0.3"}, "pnpm": {"onlyBuiltDependencies": ["bcrypt", "sharp"]}}